<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher/index';

  interface Props {
    modelValue?: string;
    disabled?: boolean;
    placeholder?: string;
  }

  interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'change', value: string, option: any): void;
    (e: 'add-subject'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    disabled: false,
    placeholder: '请选择对方会计科目',
  });

  const emit = defineEmits<Emits>();

  // 使用会计科目 hooks
  const { 
    error, 
    loading, 
    pureSubjectOptions, // 使用纯科目选项，不包含辅助核算
    refreshAccountSubjects 
  } = useAccountSubjects();

  const selectedValue = ref(props.modelValue);

  // 搜索过滤函数
  const filterOption = (input: string, option: any) => {
    const searchText = input.toLowerCase();
    return (
      option.label.toLowerCase().includes(searchText) ||
      option.code.toLowerCase().includes(searchText) ||
      option.name.toLowerCase().includes(searchText)
    );
  };

  // 选择变化处理
  const handleChange = (value: any, option?: any) => {
    const stringValue = value ? String(value) : '';
    selectedValue.value = stringValue;
    emit('update:modelValue', stringValue);

    if (option) {
      emit('change', stringValue, option);
    }
  };

  // 新增科目
  const handleAddSubject = () => {
    emit('add-subject');
  };

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      selectedValue.value = newValue;
    },
  );

  // 暴露方法给父组件
  defineExpose({
    refreshAccountSubjects,
  });
</script>

<template>
  <div class="counterpart-account-selector">
    <a-select
      v-model:value="selectedValue"
      :placeholder="placeholder"
      show-search
      allow-clear
      :disabled="disabled"
      :loading="loading"
      :options="pureSubjectOptions"
      :filter-option="filterOption"
      style="width: 100%"
      @change="handleChange"
    >
      <template #notFoundContent>
        <div v-if="loading">
          <a-spin size="small" />
          加载中...
        </div>
        <div v-else-if="error">
          {{ error }}
        </div>
        <div v-else>暂无数据</div>
      </template>
      
      <!-- 下拉框底部的新增科目按钮 -->
      <template #dropdownRender="{ menuNode }">
        <div>
          <div>{{ menuNode }}</div>
          <a-divider style="margin: 4px 0;" />
          <div
            style="padding: 4px 8px; cursor: pointer; display: flex; align-items: center; color: #1890ff;"
            @click="handleAddSubject"
          >
            <PlusOutlined style="margin-right: 4px;" />
            新增科目
          </div>
        </div>
      </template>
    </a-select>
  </div>
</template>

<style lang="scss" scoped>
.counterpart-account-selector {
  width: 100%;
}
</style>
