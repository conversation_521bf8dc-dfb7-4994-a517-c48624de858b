<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher/index';

  interface Props {
    modelValue?: string;
    disabled?: boolean;
    placeholder?: string;
    selectedAccountId?: string; // 选中的会计科目ID，用于获取对应的辅助项目
  }

  interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'change', value: string, option: any): void;
    (e: 'add-auxiliary'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    disabled: false,
    placeholder: '请选择辅助项目',
    selectedAccountId: '',
  });

  const emit = defineEmits<Emits>();

  // 使用会计科目 hooks
  const { 
    accountSubjects,
    assistantAccounting,
    getAssistantOptions,
    loading 
  } = useAccountSubjects();

  const selectedValue = ref(props.modelValue);

  // 获取当前选中科目的信息
  const selectedAccount = computed(() => {
    if (!props.selectedAccountId || !accountSubjects.value) return null;
    
    return accountSubjects.value.find(subject => 
      subject.id === props.selectedAccountId || 
      subject.value === props.selectedAccountId
    );
  });

  // 判断是否开启了辅助核算
  const isAuxiliaryEnabled = computed(() => {
    if (!selectedAccount.value) return false;
    return selectedAccount.value.useAssistant === true || 
           selectedAccount.value.assistantType;
  });

  // 获取辅助项目选项
  const auxiliaryOptions = computed(() => {
    if (!isAuxiliaryEnabled.value || !selectedAccount.value) {
      return [];
    }

    // 获取该科目的辅助核算选项
    const assistantType = selectedAccount.value.assistantType;
    if (assistantType && assistantAccounting.value) {
      const options = getAssistantOptions(assistantType);
      
      // 添加"无辅助项目"选项
      return [
        { label: '无辅助项目', value: '', code: '', name: '无辅助项目' },
        ...options
      ];
    }

    return [{ label: '无辅助项目', value: '', code: '', name: '无辅助项目' }];
  });

  // 显示的占位符文本
  const displayPlaceholder = computed(() => {
    if (!props.selectedAccountId) {
      return '请先选择会计科目';
    }
    if (!isAuxiliaryEnabled.value) {
      return '未开启辅助核算';
    }
    return props.placeholder;
  });

  // 是否禁用选择框
  const isDisabled = computed(() => {
    return props.disabled || !props.selectedAccountId || !isAuxiliaryEnabled.value;
  });

  // 搜索过滤函数
  const filterOption = (input: string, option: any) => {
    const searchText = input.toLowerCase();
    return (
      option.label.toLowerCase().includes(searchText) ||
      (option.code && option.code.toLowerCase().includes(searchText)) ||
      (option.name && option.name.toLowerCase().includes(searchText))
    );
  };

  // 选择变化处理
  const handleChange = (value: any, option?: any) => {
    const stringValue = value ? String(value) : '';
    selectedValue.value = stringValue;
    emit('update:modelValue', stringValue);

    if (option) {
      emit('change', stringValue, option);
    }
  };

  // 新增辅助项目
  const handleAddAuxiliary = () => {
    emit('add-auxiliary');
  };

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      selectedValue.value = newValue;
    },
  );

  // 监听选中科目变化，清空辅助项目选择
  watch(
    () => props.selectedAccountId,
    () => {
      selectedValue.value = '';
      emit('update:modelValue', '');
    },
  );
</script>

<template>
  <div class="auxiliary-project-selector">
    <a-select
      v-model:value="selectedValue"
      :placeholder="displayPlaceholder"
      show-search
      allow-clear
      :disabled="isDisabled"
      :loading="loading"
      :options="auxiliaryOptions"
      :filter-option="filterOption"
      style="width: 100%"
      @change="handleChange"
    >
      <template #notFoundContent>
        <div v-if="loading">
          <a-spin size="small" />
          加载中...
        </div>
        <div v-else-if="!props.selectedAccountId">
          请先选择会计科目
        </div>
        <div v-else-if="!isAuxiliaryEnabled">
          未开启辅助核算
        </div>
        <div v-else>暂无数据</div>
      </template>
      
      <!-- 下拉框底部的新增辅助项目按钮 -->
      <template #dropdownRender="{ menuNode }">
        <div v-if="isAuxiliaryEnabled && props.selectedAccountId">
          <div>{{ menuNode }}</div>
          <a-divider style="margin: 4px 0;" />
          <div
            style="padding: 4px 8px; cursor: pointer; display: flex; align-items: center; color: #1890ff;"
            @click="handleAddAuxiliary"
          >
            <PlusOutlined style="margin-right: 4px;" />
            新增辅助项目
          </div>
        </div>
        <div v-else>
          {{ menuNode }}
        </div>
      </template>
    </a-select>
  </div>
</template>

<style lang="scss" scoped>
.auxiliary-project-selector {
  width: 100%;
}
</style>
